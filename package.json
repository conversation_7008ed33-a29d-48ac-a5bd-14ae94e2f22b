{"name": "slack-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.37.0", "@convex-dev/auth": "^0.0.87", "@emoji-mart/data": "^1.2.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "convex": "^1.24.8", "date-fns": "^4.1.0", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.13.2", "input-otp": "^1.4.2", "jotai": "^2.12.5", "lucide-react": "^0.513.0", "next": "15.3.3", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "quill": "^2.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.3", "react-use": "^17.6.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}