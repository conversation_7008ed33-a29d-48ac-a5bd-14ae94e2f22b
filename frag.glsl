uniform sampler2D tNoise;
uniform float uNoiseScale;
uniform float uNoiseSpeed;
uniform float uNoiseIntensity;
uniform float uTime;
varying vec2 vUv;


void main(){
    vec2 uv=vUv;
    float time=uTime*uNoiseSpeed;
    uv-=.5;
    uv*=uNoiseScale;
    uv+=.5;
    float noise=texture2D(tNoise,uv*vec2(1.,.46)+vec2(time,time*.323)).r;
    noise+=texture2D(tNoise,uv*vec2(.5,.25)+vec2(-time*.77,-time*.414)).r;
    float circularGradient=0.-clamp(length(uv-.5)*1.,0.,1.);
    circularGradient=pow(circularGradient,2.);
    vec3 color=vec3(1.);
    float alpha=circularGradient*noise*uNoiseIntensity;
    gl_FragColor=vec4(color,alpha);
}
