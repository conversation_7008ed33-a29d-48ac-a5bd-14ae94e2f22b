
uniform sampler2D tNoise;

uniform float uCenterFade;
uniform float uCameraFade;
uniform float uTime;

varying vec2 vUv;
varying vec3 vPos;
varying vec4 vMvPos;

void main(){
    vec3 color=vec3(1.);
    float alpha=1.;
    
    // fade near camera
    alpha*=smoothstep(length(vMvPos),uCameraFade,1.);
    
    // fade near center
    alpha*=smoothstep(uCenterFade,1.,length(vPos));
    
    float shape=1.-length(gl_PointCoord-.5)*2.;
    alpha*=shape;
    
    alpha=clamp(alpha,0.,1.);
    
    gl_FragColor=vec4(color,alpha);
    
    #include<tonemapping_fragment>
    #include<colorspace_fragment>
}

