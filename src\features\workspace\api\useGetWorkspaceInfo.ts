import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Id } from "../../../../convex/_generated/dataModel";

interface GetWorkspaceProps {
  workspaceId: Id<"workspaces">;
}

interface workspaceStruct {
  Data:{
    name:string;
    isMember:boolean;
  } | null;
  IsLoading:boolean;
}


export const useGetWorkspaceInfo = ({ workspaceId }: GetWorkspaceProps) : workspaceStruct => {
  try {
    const Data = useQuery(api.workspaces.getInfo, { workspaceId });

    let IsLoading;

    if (typeof Data === "undefined") {
      IsLoading = true;
    } else {
      IsLoading = false;
    }

    // Handle the case where backend returns [] for unauthorized users
    const normalizedData = Array.isArray(Data) ? null : Data;

    return { Data: normalizedData, IsLoading };
  } catch {
    return { Data: null, IsLoading: false };
  }
};
