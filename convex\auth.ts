import GitHub from "@auth/core/providers/github";
import Google from "@auth/core/providers/google";
import { Password} from "@convex-dev/auth/providers/Password";
import { convexAuth } from "@convex-dev/auth/server";
import {DataModel} from './_generated/dataModel'

export const CustomPasswordProvider = Password<DataModel>({
  profile(params){
    return {
      name:params.name as string,
      email:params.email as string
    }
  }
})

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [
    GitHub,
    Google,
    CustomPasswordProvider
  ],
});
